#!/bin/bash

# 批量处理所有新闻文件的脚本
# 使用优化后的 news_symbol_matcher.py 处理 /root/news_train/news_data 中的所有CSV文件

set -e  # 遇到错误时退出

# 配置路径
NEWS_DATA_DIR="/root/news_train/news_data"
OUTPUT_DIR="/root/news_train/process/filter_news"
SCRIPT_DIR="/root/news_train/process/train_process"
BINANCE_SYMBOLS="/root/news_train/process/binance_symbols.json"
CRYPTO_ALIASES="/root/news_train/process/alias_symbols_processed.txt"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要文件和目录
check_prerequisites() {
    log_info "检查必要文件和目录..."
    
    if [ ! -d "$NEWS_DATA_DIR" ]; then
        log_error "新闻数据目录不存在: $NEWS_DATA_DIR"
        exit 1
    fi
    
    if [ ! -f "$SCRIPT_DIR/news_symbol_matcher.py" ]; then
        log_error "新闻符号匹配器脚本不存在: $SCRIPT_DIR/news_symbol_matcher.py"
        exit 1
    fi
    
    if [ ! -f "$BINANCE_SYMBOLS" ]; then
        log_error "币安符号文件不存在: $BINANCE_SYMBOLS"
        exit 1
    fi
    
    if [ ! -f "$CRYPTO_ALIASES" ]; then
        log_error "加密货币别名文件不存在: $CRYPTO_ALIASES"
        exit 1
    fi
    
    # 创建输出目录
    mkdir -p "$OUTPUT_DIR"
    
    log_success "所有必要文件和目录检查完成"
}

# 获取所有新闻文件
get_news_files() {
    find "$NEWS_DATA_DIR" -name "panews_flash_*.csv" | sort
}

# 处理单个文件
process_file() {
    local input_file="$1"
    local filename=$(basename "$input_file")
    
    log_info "处理文件: $filename"
    
    # 切换到脚本目录
    cd "$SCRIPT_DIR"
    
    # 执行处理
    if python3 news_symbol_matcher.py \
        --input "$input_file" \
        --output-dir "$OUTPUT_DIR" \
        --binance-symbols "$BINANCE_SYMBOLS" \
        --crypto-aliases "$CRYPTO_ALIASES"; then
        log_success "完成处理: $filename"
        return 0
    else
        log_error "处理失败: $filename"
        return 1
    fi
}

# 显示统计信息
show_statistics() {
    local total_files="$1"
    local processed_files="$2"
    local failed_files="$3"
    
    echo
    echo "=================================="
    echo "           处理统计"
    echo "=================================="
    echo "总文件数:     $total_files"
    echo "成功处理:     $processed_files"
    echo "处理失败:     $failed_files"
    echo "成功率:       $(( processed_files * 100 / total_files ))%"
    echo "=================================="
    
    # 显示输出目录信息
    if [ -d "$OUTPUT_DIR" ]; then
        local output_files=$(find "$OUTPUT_DIR" -name "*_filtered.csv" | wc -l)
        echo "生成的过滤文件数: $output_files"
        echo "输出目录: $OUTPUT_DIR"
    fi
}

# 主函数
main() {
    echo "========================================"
    echo "    新闻数据批量处理脚本"
    echo "========================================"
    echo "数据目录: $NEWS_DATA_DIR"
    echo "输出目录: $OUTPUT_DIR"
    echo "========================================"
    echo
    
    # 检查先决条件
    check_prerequisites
    
    # 获取所有新闻文件
    log_info "扫描新闻文件..."
    news_files=($(get_news_files))
    total_files=${#news_files[@]}
    
    if [ $total_files -eq 0 ]; then
        log_warning "未找到任何新闻文件"
        exit 0
    fi
    
    log_info "找到 $total_files 个新闻文件"
    echo
    
    # 处理文件
    processed_files=0
    failed_files=0
    failed_list=()
    
    for file in "${news_files[@]}"; do
        if process_file "$file"; then
            ((processed_files++))
        else
            ((failed_files++))
            failed_list+=("$(basename "$file")")
        fi
        
        # 显示进度
        echo "进度: $((processed_files + failed_files))/$total_files"
        echo
    done
    
    # 显示统计信息
    show_statistics "$total_files" "$processed_files" "$failed_files"
    
    # 显示失败的文件
    if [ $failed_files -gt 0 ]; then
        echo
        log_warning "以下文件处理失败:"
        for failed_file in "${failed_list[@]}"; do
            echo "  - $failed_file"
        done
    fi
    
    echo
    if [ $failed_files -eq 0 ]; then
        log_success "所有文件处理完成！"
    else
        log_warning "处理完成，但有 $failed_files 个文件失败"
    fi
}

# 处理命令行参数
case "${1:-}" in
    --help|-h)
        echo "用法: $0 [选项]"
        echo
        echo "选项:"
        echo "  --help, -h     显示此帮助信息"
        echo "  --dry-run      仅显示将要处理的文件，不实际处理"
        echo "  --force        强制重新处理已存在的文件"
        echo
        echo "环境变量:"
        echo "  NEWS_DATA_DIR  新闻数据目录 (默认: /root/news_train/news_data)"
        echo "  OUTPUT_DIR     输出目录 (默认: /root/news_train/process/filter_news)"
        exit 0
        ;;
    --dry-run)
        log_info "干运行模式 - 仅显示将要处理的文件"
        news_files=($(get_news_files))
        echo "将要处理的文件 (${#news_files[@]} 个):"
        for file in "${news_files[@]}"; do
            echo "  - $(basename "$file")"
        done
        exit 0
        ;;
    --force)
        log_info "强制模式 - 将重新处理已存在的文件"
        # 这里可以添加 --force 参数到 python 脚本调用中
        ;;
esac

# 执行主函数
main "$@"
