#!/usr/bin/env python3
"""
新闻-代币符号匹配器 (生产版本)
简化设计，重点关注：
1. 标题符号提取为主要判断依据
2. 内容一致性验证
3. 特殊模式加分
    usage example:
        python news_symbol_matcher.py --input /root/news_train/news_data/panews_flash_20250505.csv --output-dir /root/news_train/process/filter_news
"""

import os
import json
import re
import csv
import time
from datetime import datetime, timedelta
import pytz
from typing import Dict, List, Tuple
from crypto_text_processor import CryptoNewsTextProcessor

class NewsSymbolMatcher:
    def __init__(self, binance_symbols_path: str, crypto_aliases_path: str):
        """初始化匹配器"""
        self.binance_symbols = self._load_binance_symbols(binance_symbols_path)
        self.crypto_aliases = self._load_crypto_aliases(crypto_aliases_path)
        self.symbol_keywords = self._build_symbol_keywords()
        self.text_processor = CryptoNewsTextProcessor()
        self.MAX_SYMBOLS_PER_NEWS = 3
        
    def _load_binance_symbols(self, path: str) -> Dict:
        """加载币安交易对数据"""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error loading Binance symbols: {e}")
            return {}
    
    def _load_crypto_aliases(self, path: str) -> Dict:
        """加载加密货币别名数据"""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                content = f.read()
                # 移除注释行
                lines = content.split('\n')
                cleaned_lines = []
                for line in lines:
                    if line.strip().startswith('#'):
                        continue
                    if '#' in line:
                        line = line.split('#')[0].rstrip()
                    cleaned_lines.append(line)
                cleaned_content = '\n'.join(cleaned_lines)
                return json.loads(cleaned_content)
        except Exception as e:
            print(f"Error loading crypto aliases: {e}")
            return {}

    def _build_symbol_keywords(self) -> Dict[str, str]:
        """构建符号关键词映射，从binance_symbols.json和alias_symbols_processed.txt获取"""
        keywords = {}

        # 1. 从别名文件加载
        for alias, symbol in self.crypto_aliases.items():
            alias_lower = alias.lower()
            symbol_upper = symbol.upper()

            # 检查直接的USDT交易对
            binance_symbol = f"{symbol_upper}USDT"
            if binance_symbol in self.binance_symbols:
                keywords[alias_lower] = binance_symbol
                continue

            # 检查带前缀的交易对
            for prefix in ['1000', '1000000', '1M']:
                prefixed_symbol = f"{prefix}{symbol_upper}USDT"
                if prefixed_symbol in self.binance_symbols:
                    keywords[alias_lower] = prefixed_symbol
                    break

        # 2. 从币安交易对提取基础符号
        excluded_words = {
            'the', 'and', 'or', 'but', 'for', 'with', 'from', 'to', 'in', 'on', 'at', 'alpha',
            'by', 'up', 'out', 'off', 'over', 'under', 'get', 'set', 'put', 'go', 'come', 'see',
            'ip', 'gas', 'io', 'stop', 'run', 'win', 'play', 'game', 'time', 'work', 'life',
            'ai', 'hive digital', 'portal ventures', 'ventures', 'digital', 'labs', 'capital',
            'fund', 'group', 'team', 'inc', 'ltd', 'corp', 'company'
        }

        for symbol in self.binance_symbols.keys():
            if symbol.endswith('USDT'):
                base_asset = symbol.replace('USDT', '')
                clean_base_asset = base_asset

                # 处理特殊前缀
                if base_asset.startswith('1000000'):
                    clean_base_asset = base_asset[7:]
                elif base_asset.startswith('1000'):
                    clean_base_asset = base_asset[4:]
                elif base_asset.startswith('1M'):
                    clean_base_asset = base_asset[2:]

                if (len(clean_base_asset) > 1 and
                    clean_base_asset.lower() not in excluded_words and
                    clean_base_asset.lower() not in keywords):

                    keywords[clean_base_asset.lower()] = symbol

        return keywords
    
    def extract_symbols_from_title(self, title: str) -> Dict[str, str]:
        """从标题中提取符号 - 优先处理特殊模式，然后提取完整短语"""
        # 使用文本处理器轻量化处理
        processed_title = self.text_processor.process(title)
        found_symbols = {}
        used_positions = set()  # 记录已使用的文本位置

        # 0. 预处理特殊模式（BTC token, BTCUSD等）
        special_symbols = self._preprocess_special_patterns(title)
        for symbol in special_symbols:
            if symbol in self.symbol_keywords:
                found_symbols[symbol] = self.symbol_keywords[symbol]

        # 1. 先提取中文关键词（在处理后的小写文本中）
        for keyword, binance_symbol in self.symbol_keywords.items():
            if any('\u4e00' <= char <= '\u9fff' for char in keyword):
                pos = processed_title.find(keyword)
                if pos != -1:
                    match_start = pos
                    match_end = pos + len(keyword)
                    # 检查位置是否已被使用
                    if not any(p in used_positions for p in range(match_start, match_end)):
                        found_symbols[keyword] = binance_symbol
                        used_positions.update(range(match_start, match_end))

        # 2. 提取英文短语（优先级最高）
        english_phrases = self._extract_english_phrases(title, processed_title)
        for phrase, start, end in english_phrases:
            if phrase in self.symbol_keywords:
                # 检查位置是否已被使用
                if not any(p in used_positions for p in range(start, end)):
                    found_symbols[phrase] = self.symbol_keywords[phrase]
                    used_positions.update(range(start, end))
            else:
                # 即使短语不在关键词库中，也要标记这个位置为已使用
                # 防止短语中的单词被单独匹配（如Portal Ventures中的portal）
                used_positions.update(range(start, end))

        # 3. 提取单个英文单词（在处理后的小写文本中）
        for keyword, binance_symbol in self.symbol_keywords.items():
            # 跳过中文关键词和已匹配的关键词
            if (any('\u4e00' <= char <= '\u9fff' for char in keyword) or
                keyword in found_symbols):
                continue

            # 使用增强匹配模式处理中英文混合文本
            patterns = [
                r'\b' + re.escape(keyword) + r'\b',  # 标准词边界
                r'(?<![A-Za-z])' + re.escape(keyword) + r'(?![A-Za-z])',  # 字母边界
                r'(?<![A-Za-z0-9])' + re.escape(keyword) + r'(?![A-Za-z0-9])'  # 字母数字边界
            ]

            matched = False
            for pattern in patterns:
                try:
                    for match in re.finditer(pattern, processed_title):
                        match_start = match.start()
                        match_end = match.end()
                        # 只有当这个位置没有被短语占用时才提取
                        if not any(p in used_positions for p in range(match_start, match_end)):
                            found_symbols[keyword] = binance_symbol
                            used_positions.update(range(match_start, match_end))
                            matched = True
                            break  # 只匹配第一个出现的位置
                    if matched:
                        break
                except re.error:
                    continue

        return found_symbols

    def _extract_english_phrases(self, original_text: str, processed_text: str) -> List[Tuple[str, int, int]]:
        """从原始文本中提取英文短语，但在处理后的文本中查找位置"""
        phrases = []

        # 在原始文本中提取短语（保持大小写）
        multi_word_pattern = r'([A-Z][a-zA-Z0-9]*(?:\s+[A-Z][a-zA-Z0-9]*){1,3})(?=[\s\u4e00-\u9fff：，。！？；]|$)'

        for match in re.finditer(multi_word_pattern, original_text):
            phrase = match.group(1).lower()
            # 在处理后的文本中查找对应位置
            phrase_in_processed = phrase.replace(' ', ' ')  # 保持空格
            pos = processed_text.find(phrase_in_processed)
            if pos != -1:
                phrases.append((phrase, pos, pos + len(phrase_in_processed)))

        return phrases

    def _preprocess_special_patterns(self, text: str) -> List[str]:
        """预处理特殊模式，提取核心代币符号"""
        additional_keywords = []

        # 定义特殊模式：代币符号 + 后缀
        special_patterns = [
            r'([A-Z]{2,})\s*token',      # BTC token -> btc
            r'([A-Z]{2,})\s*coin',       # BTC coin -> btc
            r'([A-Z]{2,})\s*network',    # BTC network -> btc
            r'([A-Z]{2,})\s*protocol',   # BTC protocol -> btc
            r'([A-Z]{2,})USD(?:T)?',     # BTCUSD, BTCUSDT -> btc
            r'([A-Z]{2,})\s*u本位',      # BTC U本位 -> btc
        ]

        for pattern in special_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                core_symbol = match.group(1).lower()

                # (1) 检查是否在关键词库中
                if core_symbol in self.symbol_keywords:
                    additional_keywords.append(core_symbol)

                # (2) 检查是否在binance symbols中
                binance_symbol = core_symbol.upper() + 'USDT'
                if binance_symbol in self.binance_symbols:
                    additional_keywords.append(core_symbol)

        return list(set(additional_keywords))  # 去重


    
    def verify_symbol_in_content(self, symbol_keyword: str, content: str) -> int:
        """验证符号在内容中的出现次数"""
        content_lower = content.lower()
        
        if any('\u4e00' <= char <= '\u9fff' for char in symbol_keyword):
            # 中文关键词
            count = content_lower.count(symbol_keyword)
        else:
            # 英文关键词，使用词边界匹配
            pattern = r'\b' + re.escape(symbol_keyword) + r'\b'
            matches = re.findall(pattern, content_lower)
            count = len(matches)
        
        return count
    
    def calculate_special_pattern_bonus(self, keyword: str, title: str, content: str) -> float:
        """检查特殊模式并给予额外分数"""
        full_text = (title + " " + content).lower()
        bonus = 0.0

        # 特殊模式列表
        special_patterns = [
            f'{keyword}token',      # **token
            f'{keyword}coin',       # **coin
            f'{keyword}代币',       # **代币
            f'{keyword}空投',       # **空投
            f'空投{keyword}',       # 空投**
            f'{keyword}解锁',       # **解锁
            f'{keyword}network',    # **network
            f'{keyword}protocol',   # **protocol
            f'{keyword}usd',        # **usd
            f'{keyword}usdt',       # **usdt
            f'{keyword}u本位',      # **U本位
            f'u本位{keyword}',      # U本位**
        ]

        for pattern in special_patterns:
            if pattern in full_text:
                if '空投' in pattern or '解锁' in pattern:
                    bonus += 3.0  # 空投和解锁给更高分数
                elif 'token' in pattern or '代币' in pattern or 'u本位' in pattern:
                    bonus += 2.0  # token、代币、U本位给高分数
                else:
                    bonus += 1.0  # 其他模式给基础分数

        return bonus
    
    def calculate_score(self, keyword: str, title: str, content: str) -> float:
        """计算符号相关性评分"""
        # 基础分数：标题中出现 = 3分
        score = 3.0
        
        # 内容验证：内容中出现次数
        content_count = self.verify_symbol_in_content(keyword, content)
        if content_count >= 1:
            score += content_count * 2.0  # 内容中每次出现+2分
        else:
            # 如果内容中没有出现，降分
            score -= 2.0
        
        # 特殊模式奖励
        pattern_bonus = self.calculate_special_pattern_bonus(keyword, title, content)
        score += pattern_bonus
        
        return score
    
    def match_news_to_symbols(self, title: str, content: str, news_timestamp: int) -> tuple:
        """
        匹配新闻到代币符号
        返回: (matched_symbols, should_skip, skip_reason)
        """
        # 步骤1: 从标题提取符号
        title_symbols = self.extract_symbols_from_title(title)

        if not title_symbols:
            return [], False, ""  # 标题中没有符号，不匹配

        # 步骤2: 对标题中的符号进行评分
        symbol_scores = {}

        for keyword, binance_symbol in title_symbols.items():
            # 检查时间窗口
            if not self._is_symbol_active(binance_symbol, news_timestamp):
                continue

            # 计算评分
            score = self.calculate_score(keyword, title, content)

            # 只保留正分的符号
            if score > 0:
                if binance_symbol not in symbol_scores or score > symbol_scores[binance_symbol]:
                    symbol_scores[binance_symbol] = score

        # 步骤3: 选择最佳符号
        if not symbol_scores:
            return [], False, ""

        # 如果只有一个符号，直接返回
        if len(symbol_scores) == 1:
            binance_symbol = list(symbol_scores.keys())[0]
            base_symbol = binance_symbol.replace('USDT', '')
            final_format = f"{base_symbol}-PERP.BINANCE"
            return [final_format], False, ""

        # 如果有多个符号，按分数排序，取前几名
        sorted_symbols = sorted(symbol_scores.items(), key=lambda x: x[1], reverse=True)
        top_symbols = [symbol for symbol, score in sorted_symbols[:self.MAX_SYMBOLS_PER_NEWS]]

        # 转换为最终格式
        final_symbols = []
        for binance_symbol in top_symbols:
            base_symbol = binance_symbol.replace('USDT', '')
            final_format = f"{base_symbol}-PERP.BINANCE"
            final_symbols.append(final_format)

        return final_symbols, False, ""


    
    def _is_symbol_active(self, symbol: str, timestamp: int) -> bool:
        """检查代币在指定时间是否活跃交易"""
        if symbol not in self.binance_symbols:
            return False
        
        data = self.binance_symbols[symbol]
        onboard_ts = data.get('onboardDate', 0)
        delivery_ts = data.get('deliveryDate', 0)
        
        if timestamp < onboard_ts:
            return False
        
        if delivery_ts > 0 and timestamp > delivery_ts:
            return False
        
        return True

    def _parse_news_timestamp(self, row: Dict, input_file: str) -> int:
        """
        解析新闻时间戳，返回纳秒级时间戳
        完全依赖文件名解析日期，避免date字段缺失问题

        Args:
            row: CSV行数据
            input_file: 输入文件路径

        Returns:
            int: 纳秒级UTC时间戳
        """
        try:
            # 从文件名提取日期 (格式: panews_flash_20250101.csv)
            filename = os.path.basename(input_file)
            date_str = filename.split("_")[-1].split(".")[0]  # 20250101

            # 验证日期格式
            if len(date_str) != 8 or not date_str.isdigit():
                raise ValueError(f"文件名中的日期格式不正确: {date_str}")

            # 提取年月日
            year = int(date_str[:4])
            month = int(date_str[4:6])
            day = int(date_str[6:8])

            # 获取时间字符串，如果缺失则使用默认值
            time_str = row.get('time', '00:00').strip()
            if not time_str:
                time_str = '00:00'

            # 解析时间 (支持 HH:MM 和 HH:MM:SS 格式)
            if ':' in time_str:
                time_parts = time_str.split(':')
                hour = int(time_parts[0])
                minute = int(time_parts[1])
                second = int(time_parts[2]) if len(time_parts) > 2 else 0
            else:
                # 如果时间格式异常，使用默认值
                hour, minute, second = 0, 0, 0

            # 创建datetime对象 (假设为北京时间)
            datetime_obj = datetime(year, month, day, hour, minute, second)

            # 设置为北京时区
            beijing_tz = pytz.timezone('Asia/Shanghai')
            aware_dt_beijing = beijing_tz.localize(datetime_obj)

            # 转换为UTC时区
            aware_dt_utc = aware_dt_beijing.astimezone(pytz.utc)

            # 返回纳秒级时间戳
            return int(aware_dt_utc.timestamp() * 1_000_000_000)

        except Exception as e:
            raise ValueError(f"无法解析时间 '{row.get('time', 'N/A')}' 和文件名日期 '{date_str}': {e}")

    def process_news_file(
        self,
        input_file: str,
        output_dir: str,
        force_reprocess: bool = False
    ) -> Dict:
        """处理新闻文件，保存到filter_news文件夹"""
        # 创建输出目录
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # 生成输出文件名
        input_filename = os.path.basename(input_file)
        base_name = input_filename.replace('.csv', '')
        output_file = os.path.join(output_dir, f"{base_name}_filtered.csv")
        skipped_file = os.path.join(output_dir, f"{base_name}_skipped.csv")

        # 检查文件是否已存在
        if not force_reprocess and os.path.exists(output_file):
            print(f"⏭️  文件已存在，跳过处理: {output_file}")
            return {
                'total_news': 0,
                'matched_news': 0,
                'skipped_news': 0,
                'no_symbol_news': 0,
                'symbols_found': [],
                'processing_time': 0,
                'match_rate': 0,
                'output_file': output_file,
                'skipped_file': None,
                'already_processed': True
            }

        stats = {
            'total_news': 0,
            'matched_news': 0,
            'skipped_news': 0,
            'no_symbol_news': 0,
            'symbols_found': set(),
            'processing_time': 0
        }

        start_time = time.time()
        skipped_rows = []

        with open(input_file, 'r', encoding='utf-8-sig') as infile:
            reader = csv.DictReader(infile)
            fieldnames = reader.fieldnames + ['matched_symbols', 'timestamp_ns']

            matched_rows = []

            for row in reader:
                stats['total_news'] += 1

                title = row['title']
                content = row['content']

                # 解析时间戳
                try:
                    news_timestamp_ns = self._parse_news_timestamp(row, input_file)
                    news_ts = news_timestamp_ns // 1_000_000  # 转换为毫秒用于兼容性检查
                except Exception as e:
                    print(f"时间解析错误: {e}, 跳过该条新闻")
                    continue

                # 匹配符号
                symbols, should_skip, skip_reason = self.match_news_to_symbols(title, content, news_ts)

                if should_skip:
                    # 需要跳过的新闻
                    stats['skipped_news'] += 1
                    row['skip_reason'] = skip_reason
                    skipped_rows.append(row)
                elif symbols:
                    # 有匹配符号的新闻
                    stats['matched_news'] += 1
                    stats['symbols_found'].update(symbols)
                    row['matched_symbols'] = ','.join(symbols)
                    row['timestamp_ns'] = news_timestamp_ns
                    matched_rows.append(row)
                else:
                    # 没有符号的新闻，不保存
                    stats['no_symbol_news'] += 1

        # 写入匹配的新闻
        if matched_rows:
            with open(output_file, 'w', encoding='utf-8-sig', newline='') as outfile:
                writer = csv.DictWriter(outfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(matched_rows)

        # 写入跳过的新闻
        if skipped_rows:
            skipped_fieldnames = reader.fieldnames + ['skip_reason']
            with open(skipped_file, 'w', encoding='utf-8-sig', newline='') as skipfile:
                writer = csv.DictWriter(skipfile, fieldnames=skipped_fieldnames)
                writer.writeheader()
                writer.writerows(skipped_rows)

        end_time = time.time()
        stats['processing_time'] = end_time - start_time
        stats['symbols_found'] = list(stats['symbols_found'])
        stats['match_rate'] = stats['matched_news'] / stats['total_news'] if stats['total_news'] > 0 else 0
        stats['output_file'] = output_file
        stats['skipped_file'] = skipped_file if skipped_rows else None

        return stats

def main():
    """主函数 - 处理新闻文件"""
    import argparse

    parser = argparse.ArgumentParser(description='新闻-代币符号匹配器')
    parser.add_argument('--input', required=True, help='输入新闻CSV文件')
    parser.add_argument('--output-dir', default='filter_news', help='输出目录 (默认: filter_news)')
    parser.add_argument('--binance-symbols', default='../binance_symbols.json', help='币安符号文件')
    parser.add_argument('--crypto-aliases', default='alias_symbols_processed.txt', help='加密货币别名文件')
    parser.add_argument('--force', action='store_true', help='强制重新处理已存在的文件')

    args = parser.parse_args()

    # 初始化匹配器
    matcher = NewsSymbolMatcher(args.binance_symbols, args.crypto_aliases)

    # 处理文件
    print(f"开始处理: {args.input}")
    stats = matcher.process_news_file(args.input, args.output_dir, args.force)

    # 显示结果
    print(f"✅ 处理完成!")
    print(f"总新闻数: {stats['total_news']}")
    print(f"匹配新闻数: {stats['matched_news']}")
    print(f"跳过新闻数: {stats['skipped_news']}")
    print(f"无符号新闻数: {stats['no_symbol_news']}")
    print(f"匹配率: {stats['match_rate']:.1%}")
    print(f"发现符号数: {len(stats['symbols_found'])}")
    print(f"处理时间: {stats['processing_time']:.2f}秒")
    print(f"匹配结果文件: {stats['output_file']}")
    if stats['skipped_file']:
        print(f"跳过新闻文件: {stats['skipped_file']}")

if __name__ == "__main__":
    main()
