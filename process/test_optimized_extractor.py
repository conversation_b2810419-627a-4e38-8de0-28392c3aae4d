#!/usr/bin/env python3
"""
测试优化后的news_instrument_extractor_v2.py
验证与news_symbol_matcher.py的一致性

作者：AI Assistant
日期：2025年7月27日
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from news_instrument_extractor_v2 import NewsInstrumentExtractor
from news_symbol_matcher import NewsSymbolMatcher
from nautilus_trader.model.identifiers import Venue
import json


def test_symbol_extraction_consistency():
    """测试两个提取器的一致性"""
    print("🔬 测试Symbol提取一致性")
    print("="*60)
    
    # 初始化两个提取器
    print("初始化提取器...")
    
    # news_symbol_matcher
    matcher = NewsSymbolMatcher(
        binance_symbols_path='binance_symbols.json',
        crypto_aliases_path='alias_symbols_processed.txt'
    )

    # news_instrument_extractor_v2 (优化版)
    extractor = NewsInstrumentExtractor(
        cache=None,
        venue=Venue("BINANCE"),
        max_symbols=3,
        binance_symbols_path='binance_symbols.json',
        crypto_aliases_path='alias_symbols_processed.txt'
    )

    # 加载可用符号
    with open('binance_symbols.json', 'r') as f:
        binance_data = json.load(f)
    available_symbols = set(binance_data.keys())
    extractor._set_available_symbols(available_symbols)
    
    # 测试用例
    test_cases = [
        {
            'title': 'BTC突破65000美元，市场情绪高涨',
            'content': '比特币价格今日突破65000美元大关，交易量激增。分析师认为这是牛市的开始。',
            'description': '中文BTC测试'
        },
        {
            'title': 'Ethereum 2.0 Upgrade Completed Successfully',
            'content': 'The Ethereum network has successfully completed its major upgrade. ETH price surged 15% following the news.',
            'description': '英文ETH测试'
        },
        {
            'title': 'DOGE空投活动开始，用户可获得免费代币',
            'content': '狗狗币官方宣布空投活动，持有DOGE的用户将获得额外奖励。',
            'description': '空投模式测试'
        },
        {
            'title': 'Portal Ventures投资新项目，PORTAL代币上涨',
            'content': 'Portal Ventures宣布投资区块链项目，PORTAL token价格应声上涨20%。',
            'description': '英文短语测试'
        },
        {
            'title': 'BTCUSDT合约交易量创新高',
            'content': 'BTC U本位合约交易量达到历史新高，显示市场活跃度提升。',
            'description': '特殊模式测试'
        }
    ]
    
    print(f"测试 {len(test_cases)} 个用例...")
    print()
    
    for i, case in enumerate(test_cases, 1):
        print(f"📝 测试用例 {i}: {case['description']}")
        print(f"标题: {case['title']}")
        print(f"内容: {case['content'][:50]}...")
        
        # news_symbol_matcher结果
        matcher_symbols, _, _ = matcher.match_news_to_symbols(
            case['title'], case['content'], 1640995200000  # 2022-01-01的时间戳
        )
        
        # news_instrument_extractor_v2结果
        extractor_results = extractor.extract_symbols_from_text(
            case['title'].lower(), case['content'].lower()
        )
        extractor_symbols = [result[0] + '-PERP.BINANCE' for result in extractor_results]
        
        print(f"🎯 news_symbol_matcher结果: {matcher_symbols}")
        print(f"🔧 news_instrument_extractor_v2结果: {extractor_symbols}")
        
        # 比较结果
        if set(matcher_symbols) == set(extractor_symbols):
            print("✅ 结果一致")
        else:
            print("❌ 结果不一致")
            # 分析差异
            only_in_matcher = set(matcher_symbols) - set(extractor_symbols)
            only_in_extractor = set(extractor_symbols) - set(matcher_symbols)
            if only_in_matcher:
                print(f"   仅在matcher中: {only_in_matcher}")
            if only_in_extractor:
                print(f"   仅在extractor中: {only_in_extractor}")
        
        print("-" * 40)
        print()


def test_performance_comparison():
    """测试性能对比"""
    print("⚡ 性能对比测试")
    print("="*60)
    
    import time
    import pandas as pd
    
    # 加载测试数据
    try:
        df = pd.read_csv('../news_data/panews_flash_20250101.csv', encoding='utf-8')
        test_data = df.head(20)  # 取前20条进行测试
        print(f"加载测试数据: {len(test_data)} 条")
    except Exception as e:
        print(f"无法加载测试数据: {e}")
        return
    
    # 初始化提取器
    matcher = NewsSymbolMatcher(
        binance_symbols_path='../binance_symbols.json',
        crypto_aliases_path='alias_symbols_processed.txt'
    )
    
    extractor = NewsInstrumentExtractor(
        cache=None,
        venue=Venue("BINANCE"),
        max_symbols=3,
        binance_symbols_path='../binance_symbols.json',
        crypto_aliases_path='alias_symbols_processed.txt'
    )
    
    with open('../binance_symbols.json', 'r') as f:
        binance_data = json.load(f)
    available_symbols = set(binance_data.keys())
    extractor._set_available_symbols(available_symbols)
    
    # 测试news_symbol_matcher性能
    print("测试news_symbol_matcher性能...")
    start_time = time.time()
    matcher_results = []
    
    for _, row in test_data.iterrows():
        title = str(row.get('title', ''))
        content = str(row.get('content', ''))
        symbols, _, _ = matcher.match_news_to_symbols(title, content, 1640995200000)
        matcher_results.append(len(symbols))
    
    matcher_time = time.time() - start_time
    
    # 测试news_instrument_extractor_v2性能
    print("测试news_instrument_extractor_v2性能...")
    start_time = time.time()
    extractor_results = []
    
    for _, row in test_data.iterrows():
        title = str(row.get('title', '')).lower()
        content = str(row.get('content', '')).lower()
        symbols = extractor.extract_symbols_from_text(title, content)
        extractor_results.append(len(symbols))
    
    extractor_time = time.time() - start_time
    
    # 结果统计
    print(f"\n📊 性能对比结果:")
    print(f"news_symbol_matcher:")
    print(f"  处理时间: {matcher_time:.3f}秒")
    print(f"  平均每条: {matcher_time/len(test_data)*1000:.1f}ms")
    print(f"  提取到符号的新闻: {sum(1 for x in matcher_results if x > 0)}/{len(test_data)}")
    
    print(f"news_instrument_extractor_v2:")
    print(f"  处理时间: {extractor_time:.3f}秒")
    print(f"  平均每条: {extractor_time/len(test_data)*1000:.1f}ms")
    print(f"  提取到符号的新闻: {sum(1 for x in extractor_results if x > 0)}/{len(test_data)}")
    
    if extractor_time < matcher_time:
        speedup = matcher_time / extractor_time
        print(f"🚀 优化版提速: {speedup:.1f}倍")
    else:
        slowdown = extractor_time / matcher_time
        print(f"⚠️ 优化版较慢: {slowdown:.1f}倍")


def test_symbol_keywords_coverage():
    """测试符号关键词覆盖率"""
    print("📈 符号关键词覆盖率测试")
    print("="*60)
    
    # 初始化提取器
    matcher = NewsSymbolMatcher(
        binance_symbols_path='../binance_symbols.json',
        crypto_aliases_path='alias_symbols_processed.txt'
    )
    
    extractor = NewsInstrumentExtractor(
        cache=None,
        venue=Venue("BINANCE"),
        max_symbols=3,
        binance_symbols_path='../binance_symbols.json',
        crypto_aliases_path='alias_symbols_processed.txt'
    )
    
    with open('../binance_symbols.json', 'r') as f:
        binance_data = json.load(f)
    available_symbols = set(binance_data.keys())
    extractor._set_available_symbols(available_symbols)
    
    # 比较关键词数量
    matcher_keywords = len(matcher.symbol_keywords)
    extractor_keywords = len(extractor.symbol_keywords)
    
    print(f"news_symbol_matcher关键词数量: {matcher_keywords}")
    print(f"news_instrument_extractor_v2关键词数量: {extractor_keywords}")
    
    # 检查关键词重叠
    matcher_keys = set(matcher.symbol_keywords.keys())
    extractor_keys = set(extractor.symbol_keywords.keys())
    
    overlap = matcher_keys & extractor_keys
    only_in_matcher = matcher_keys - extractor_keys
    only_in_extractor = extractor_keys - matcher_keys
    
    print(f"重叠关键词: {len(overlap)}")
    print(f"仅在matcher中: {len(only_in_matcher)}")
    print(f"仅在extractor中: {len(only_in_extractor)}")
    
    if only_in_matcher:
        print(f"仅在matcher中的关键词示例: {list(only_in_matcher)[:10]}")
    if only_in_extractor:
        print(f"仅在extractor中的关键词示例: {list(only_in_extractor)[:10]}")
    
    coverage = len(overlap) / max(matcher_keywords, extractor_keywords) * 100
    print(f"关键词覆盖率: {coverage:.1f}%")


def main():
    """主函数"""
    print("🚀 优化后的news_instrument_extractor_v2.py测试")
    print("="*80)
    print()
    
    try:
        # 1. 一致性测试
        test_symbol_extraction_consistency()
        print()
        
        # 2. 性能对比测试
        test_performance_comparison()
        print()
        
        # 3. 关键词覆盖率测试
        test_symbol_keywords_coverage()
        print()
        
        print("✅ 所有测试完成！")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
