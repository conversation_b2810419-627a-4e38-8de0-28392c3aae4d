from typing import List, Dict, Tuple, Optional
from nautilus_trader.model.identifiers import InstrumentId, Venue
import re
import json
import os
from collections import Counter
import multiprocessing as mp
from functools import lru_cache

class NewsInstrumentExtractor:
    """
    从预处理后的新闻内容中提取相关交易对的工具类
    优化版本：整合news_symbol_matcher.py的逻辑，支持别名文件和分层提取
    """

    def __init__(self, cache, venue: Venue = Venue("BINANCE"), max_symbols: int = 3,
                 confidence_params: Optional[Dict] = None, threshold_params: Optional[Dict] = None,
                 binance_symbols_path: str = None, crypto_aliases_path: str = None):
        self.cache = cache
        self.venue = venue
        self.max_symbols = max_symbols

        # 置信度计算参数
        self.confidence_params = confidence_params or {
            'base_multiplier': 0.3,           # 基础匹配次数倍数
            'trading_context_bonus': 1.2,     # 交易上下文加成
            'max_confidence': 1.0             # 最大置信度
        }

        # 阈值参数
        self.threshold_params = threshold_params or {
            'min_confidence_with_context': 0.15,    # 降低：有交易上下文时的最低置信度
            'min_confidence_without_context': 0.25, # 降低：无交易上下文时的最低置信度
            'min_match_count': 1                     # 最小匹配次数
        }

        # 加载外部数据源（与news_symbol_matcher.py保持一致）
        self.binance_symbols = self._load_binance_symbols(binance_symbols_path or '../binance_symbols.json')
        self.crypto_aliases = self._load_crypto_aliases(crypto_aliases_path or 'alias_symbols_processed.txt')

        # 构建符号关键词映射（与news_symbol_matcher.py保持一致）
        self.symbol_keywords = self._build_symbol_keywords()

        # 黑名单（扩展版本，与news_symbol_matcher.py保持一致）
        self.blacklist = {
            # 稳定币（波动性低，不适合交易）
            'usdt', 'usdc', 'busd', 'dai', 'tusd', 'frax', 'fdusd',
            # 常见英文词汇（与news_symbol_matcher.py保持一致）
            'the', 'and', 'or', 'but', 'for', 'with', 'from', 'to', 'in', 'on', 'at', 'alpha',
            'by', 'up', 'out', 'off', 'over', 'under', 'get', 'set', 'put', 'go', 'come', 'see',
            'ip', 'gas', 'io', 'stop', 'run', 'win', 'play', 'game', 'time', 'work', 'life',
            'ai', 'hive digital', 'portal ventures', 'ventures', 'digital', 'labs', 'capital',
            'fund', 'group', 'team', 'inc', 'ltd', 'corp', 'company',
            # 原有黑名单
            'news', 'market', 'price', 'trade', 'trading', 'crypto',
            # 单字符排除
            'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm',
            'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'y', 'z',
            # 数字和常见词
            'x', 'lab', 'coin', 'token', 'chain', 'network',
            # 容易误识别的短词
            'one', 'op', 'ark', 'go', 'up', 'in', 'on', 'at', 'is',
            'as', 'be', 'do', 'if', 'no', 'so', 'we', 'my', 'by', 'an',
            'date','time','title','content','http','web3','web2','rest',
            # 金融相关容易误识别的词
            'etf', 'ipo', 'ceo', 'cto', 'cfo', 'sec', 'fda', 'api'
        }

        # 保留原有的映射表作为补充
        self.legacy_symbol_mapping = {
            # 中文映射
            '比特币': 'btc', '以太坊': 'eth', '以太币': 'eth',
            '狗狗币': 'doge', '瑞波币': 'xrp', '莱特币': 'ltc',
            '柴犬币': 'shib', '艾达币': 'ada', '波卡': 'dot',

            # 英文别名映射（转为小写）
            'bitcoin': 'btc', 'ethereum': 'eth', 'dogecoin': 'doge',
            'ripple': 'xrp', 'litecoin': 'ltc', 'cardano': 'ada',
            'polkadot': 'dot', 'chainlink': 'link', 'solana': 'sol',
            'filecoin': 'fil', 'avalanche': 'avax', 'uniswap': 'uni'
        }

        # 交易相关的关键词（英文转小写）
        self.trading_keywords = {
            '买入', '卖出', '交易', '持仓', '建仓', '减仓', '清仓', '爆仓',
            '做多', '做空', '杠杆', '合约', '现货', '期货', '期权',
            '价格', '涨幅', '跌幅', '市值', '流通量', '成交量',
            '鲸鱼', '巨鲸', '大户', '散户', '机构', '基金',
            'buy', 'sell', 'trade', 'long', 'short', 'leverage',
            'price', 'volume', 'whale', 'pump', 'dump'
        }
        self.available_symbols = []
        self.base_tokens = {}  # 初始化 base_tokens 字典

        # 预编译正则表达式模式缓存
        self._compiled_patterns = {}
        self._trading_keywords_compiled = None
        self._base_token_pattern = None
        self._full_symbol_pattern = None
        self._mapped_symbol_pattern = None

    def _load_binance_symbols(self, path: str) -> Dict:
        """加载币安交易对数据（与news_symbol_matcher.py保持一致）"""
        try:
            if os.path.exists(path):
                with open(path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"Warning: Error loading Binance symbols from {path}: {e}")
        return {}

    def _load_crypto_aliases(self, path: str) -> Dict:
        """加载加密货币别名数据（与news_symbol_matcher.py保持一致）"""
        try:
            if os.path.exists(path):
                with open(path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    # 移除注释行
                    lines = content.split('\n')
                    cleaned_lines = []
                    for line in lines:
                        if line.strip().startswith('#'):
                            continue
                        if '#' in line:
                            line = line.split('#')[0].rstrip()
                        cleaned_lines.append(line)
                    cleaned_content = '\n'.join(cleaned_lines)
                    return json.loads(cleaned_content)
        except Exception as e:
            print(f"Warning: Error loading crypto aliases from {path}: {e}")
        return {}

    def _build_symbol_keywords(self) -> Dict[str, str]:
        """构建符号关键词映射（与news_symbol_matcher.py保持一致）"""
        keywords = {}

        # 1. 从别名文件加载
        for alias, symbol in self.crypto_aliases.items():
            alias_lower = alias.lower()
            symbol_upper = symbol.upper()

            # 检查直接的USDT交易对
            binance_symbol = f"{symbol_upper}USDT"
            if binance_symbol in self.binance_symbols:
                keywords[alias_lower] = binance_symbol
                continue

            # 检查带前缀的交易对
            for prefix in ['1000', '1000000', '1M']:
                prefixed_symbol = f"{prefix}{symbol_upper}USDT"
                if prefixed_symbol in self.binance_symbols:
                    keywords[alias_lower] = prefixed_symbol
                    break

        # 2. 从币安交易对提取基础符号
        excluded_words = self.blacklist

        for symbol in self.binance_symbols.keys():
            if symbol.endswith('USDT'):
                base_asset = symbol.replace('USDT', '')
                clean_base_asset = base_asset

                # 处理特殊前缀
                if base_asset.startswith('1000000'):
                    clean_base_asset = base_asset[7:]
                elif base_asset.startswith('1000'):
                    clean_base_asset = base_asset[4:]
                elif base_asset.startswith('1M'):
                    clean_base_asset = base_asset[2:]

                if (len(clean_base_asset) > 1 and
                    clean_base_asset.lower() not in excluded_words and
                    clean_base_asset.lower() not in keywords):

                    keywords[clean_base_asset.lower()] = symbol

        return keywords


    def _precompile_patterns(self):
        """预编译所有正则表达式以提高性能"""
        try:
            # 交易关键词
            trading_keywords_pattern = '|'.join(re.escape(k) for k in self.trading_keywords)
            self._trading_keywords_compiled = re.compile(trading_keywords_pattern)

            # 基础代币 (按长度降序排序以优先匹配长名称)
            if self.base_tokens:
                sorted_base_tokens = sorted([t for t in self.base_tokens.keys() if t not in self.blacklist], key=len, reverse=True)
                if sorted_base_tokens:
                    self._base_token_pattern = re.compile(r'\b(' + '|'.join(re.escape(t) for t in sorted_base_tokens) + r')\b')
                else:
                    self._base_token_pattern = re.compile(r'(?!.*)')  # 匹配空的模式

            # 完整交易对 (按长度降序排序)
            if self.available_symbols:
                sorted_symbols = sorted([s for s in self.available_symbols if s not in self.blacklist], key=len, reverse=True)
                if sorted_symbols:
                    self._full_symbol_pattern = re.compile(r'\b(' + '|'.join(re.escape(s) for s in sorted_symbols) + r')\b')
                else:
                    self._full_symbol_pattern = re.compile(r'(?!.*)')  # 匹配空的模式

            # 映射表关键词（包含legacy映射和symbol_keywords）
            mapped_patterns = []

            # 添加legacy映射
            for keyword in self.legacy_symbol_mapping.keys():
                if self.legacy_symbol_mapping[keyword] in self.blacklist:
                    continue
                if keyword.isascii():
                    mapped_patterns.append(r'\b' + re.escape(keyword) + r'\b')
                else:
                    mapped_patterns.append(re.escape(keyword))

            # 添加symbol_keywords中的关键词
            for keyword in self.symbol_keywords.keys():
                if keyword not in self.blacklist:
                    if keyword.isascii():
                        mapped_patterns.append(r'\b' + re.escape(keyword) + r'\b')
                    else:
                        mapped_patterns.append(re.escape(keyword))

            if mapped_patterns:
                self._mapped_symbol_pattern = re.compile('|'.join(mapped_patterns))
            else:
                self._mapped_symbol_pattern = re.compile(r'(?!.*)')  # 匹配空的模式

        except Exception as e:
            print(f"Warning: Error compiling patterns: {e}")
            # 设置默认的空模式
            self._trading_keywords_compiled = re.compile(r'(?!.*)')
            self._base_token_pattern = re.compile(r'(?!.*)')
            self._full_symbol_pattern = re.compile(r'(?!.*)')
            self._mapped_symbol_pattern = re.compile(r'(?!.*)')


    def _set_available_symbols(self, symbols: set[str]) -> None:
        """设置可用符号并重新构建映射"""
        self.available_symbols = [s.lower() for s in symbols]
        # 更新binance_symbols以包含传入的符号
        for symbol in symbols:
            if symbol not in self.binance_symbols:
                self.binance_symbols[symbol] = {}
        # 重新构建符号关键词映射
        self.symbol_keywords = self._build_symbol_keywords()
        self.base_tokens = self._extract_base_tokens()
        self._precompile_patterns()

    def _get_available_symbols(self) -> set[str]:
        """
        获取所有可交易的symbol（转为小写用于匹配）

        Returns:
            set[str]: 小写symbol集合
        """
        if self.cache:
            instruments = self.cache.instruments(venue=self.venue)

            if hasattr(instruments, 'values'):
                instrument_list = instruments.values()
            else:
                instrument_list = instruments

            symbols_lower = {inst.id.symbol.value.lower() for inst in instrument_list}

            self.available_symbols = list(symbols_lower)
            # 更新binance_symbols
            for symbol in symbols_lower:
                symbol_upper = symbol.upper()
                if symbol_upper not in self.binance_symbols:
                    self.binance_symbols[symbol_upper] = {}
            # 重新构建映射
            self.symbol_keywords = self._build_symbol_keywords()
            self.base_tokens = self._extract_base_tokens()
            self._precompile_patterns()
            return symbols_lower
        else:
            # 如果没有cache，使用binance_symbols
            symbols_lower = {s.lower() for s in self.binance_symbols.keys()}
            self.available_symbols = list(symbols_lower)
            self.base_tokens = self._extract_base_tokens()
            self._precompile_patterns()
            return symbols_lower
    
    def _extract_base_tokens(self) -> Dict[str, str]:
        """
        从完整symbol中提取基础代币名称，建立映射关系
        所有映射都使用小写，稳定币已在黑名单中处理

        Returns:
            Dict[str, str]: 小写基础代币 -> 小写完整symbol的映射
        """
        base_to_full = {}
        for lower_symbol in self.available_symbols:
            # 只处理USDT交易对，因为这是主要的交易对
            if lower_symbol.endswith('usdt'):
                base_token = lower_symbol[:-4]  # 移除usdt后缀

                # 处理特殊前缀的代币
                original_base_token = base_token
                normalized_base_token = self._normalize_base_token(base_token)

                # 确保基础代币不在黑名单中
                if (normalized_base_token and len(normalized_base_token) >= 2 and
                    normalized_base_token not in self.blacklist):
                    # 建立两个映射：原始代币名和标准化代币名都映射到完整symbol
                    base_to_full[original_base_token] = lower_symbol
                    if normalized_base_token != original_base_token:
                        base_to_full[normalized_base_token] = lower_symbol

        return base_to_full

    def _normalize_base_token(self, base_asset: str) -> str:
        """标准化基础代币名称，移除特殊前缀"""
        # Check for 1000000 prefix
        if base_asset.startswith('1000000'):
            return base_asset[7:]  # Remove '1000000' prefix
        # Check for 1000 prefix
        elif base_asset.startswith('1000'):
            return base_asset[4:]  # Remove '1000' prefix
        # Check for 1M prefix
        elif base_asset.startswith('1M'):
            return base_asset[2:]  # Remove '1M' prefix
        else:
            return base_asset
    
    def extract_symbols_from_text(self, preprocessed_title: str, preprocessed_content: str) -> List[Tuple[str, int, float]]:
        """
        从预处理后的新闻内容中提取交易对symbols
        完全采用news_symbol_matcher.py的分层提取策略

        Args:
            preprocessed_title: 预处理后的标题（已转小写）
            preprocessed_content: 预处理后的内容（已转小写）

        Returns:
            List[Tuple[original_case_symbol, match_count, confidence]]
        """
        # 步骤1: 从标题提取符号（使用与news_symbol_matcher.py完全相同的逻辑）
        title_symbols = self._extract_symbols_from_title_enhanced(preprocessed_title)

        if not title_symbols:
            return []  # 如果标题中没有符号，直接返回空列表

        # 步骤2: 对标题中的符号进行评分和验证
        symbol_scores = {}

        for keyword, binance_symbol in title_symbols.items():
            # 计算评分（完全基于news_symbol_matcher.py的评分逻辑）
            score = self._calculate_symbol_score(keyword, preprocessed_title, preprocessed_content)

            # 只保留正分的符号
            if score > 0:
                if binance_symbol not in symbol_scores or score > symbol_scores[binance_symbol]:
                    symbol_scores[binance_symbol] = score

        # 步骤3: 选择最佳符号
        if not symbol_scores:
            return []

        # 如果只有一个符号，直接返回
        if len(symbol_scores) == 1:
            binance_symbol = list(symbol_scores.keys())[0]
            score = symbol_scores[binance_symbol]
            upper_symbol = binance_symbol.upper()
            match_count = max(1, int(score / 3))  # 基础分数是3分
            has_trading_context = self._has_trading_context(f"{preprocessed_title} {preprocessed_content}")
            confidence = self._calculate_confidence_from_score(score, has_trading_context)
            return [(upper_symbol, match_count, confidence)]

        # 如果有多个符号，按分数排序，取前几名
        sorted_symbols = sorted(symbol_scores.items(), key=lambda x: x[1], reverse=True)
        top_symbols = sorted_symbols[:self.max_symbols]

        # 转换为最终格式
        results = []
        has_trading_context = self._has_trading_context(f"{preprocessed_title} {preprocessed_content}")

        for binance_symbol, score in top_symbols:
            upper_symbol = binance_symbol.upper()
            match_count = max(1, int(score / 3))  # 基础分数是3分
            confidence = self._calculate_confidence_from_score(score, has_trading_context)
            results.append((upper_symbol, match_count, confidence))

        return results

    def _extract_symbols_from_title_enhanced(self, title: str) -> Dict[str, str]:
        """
        从标题中提取符号 - 完全采用news_symbol_matcher.py的逻辑
        优先处理特殊模式，然后提取完整短语
        """
        found_symbols = {}
        used_positions = set()  # 记录已使用的文本位置

        # 0. 预处理特殊模式（BTC token, BTCUSD等）
        special_symbols = self._preprocess_special_patterns(title)
        for symbol in special_symbols:
            if symbol in self.symbol_keywords:
                found_symbols[symbol] = self.symbol_keywords[symbol]

        # 1. 先提取中文关键词（在处理后的小写文本中）
        for keyword, binance_symbol in self.symbol_keywords.items():
            if any('\u4e00' <= char <= '\u9fff' for char in keyword):
                pos = title.find(keyword)
                if pos != -1:
                    match_start = pos
                    match_end = pos + len(keyword)
                    # 检查位置是否已被使用
                    if not any(p in used_positions for p in range(match_start, match_end)):
                        found_symbols[keyword] = binance_symbol
                        used_positions.update(range(match_start, match_end))

        # 2. 提取英文短语（优先级最高）
        english_phrases = self._extract_english_phrases_enhanced(title)
        for phrase, start, end in english_phrases:
            if phrase in self.symbol_keywords:
                # 检查位置是否已被使用
                if not any(p in used_positions for p in range(start, end)):
                    found_symbols[phrase] = self.symbol_keywords[phrase]
                    used_positions.update(range(start, end))
            else:
                # 即使短语不在关键词库中，也要标记这个位置为已使用
                # 防止短语中的单词被单独匹配（如Portal Ventures中的portal）
                used_positions.update(range(start, end))

        # 3. 提取单个英文单词（在处理后的小写文本中）
        for keyword, binance_symbol in self.symbol_keywords.items():
            # 跳过中文关键词和已匹配的关键词
            if (any('\u4e00' <= char <= '\u9fff' for char in keyword) or
                keyword in found_symbols):
                continue

            # 使用增强匹配模式处理中英文混合文本
            patterns = [
                r'\b' + re.escape(keyword) + r'\b',  # 标准词边界
                r'(?<![A-Za-z])' + re.escape(keyword) + r'(?![A-Za-z])',  # 字母边界
                r'(?<![A-Za-z0-9])' + re.escape(keyword) + r'(?![A-Za-z0-9])'  # 字母数字边界
            ]

            matched = False
            for pattern in patterns:
                try:
                    for match in re.finditer(pattern, title):
                        match_start = match.start()
                        match_end = match.end()
                        # 只有当这个位置没有被短语占用时才提取
                        if not any(p in used_positions for p in range(match_start, match_end)):
                            found_symbols[keyword] = binance_symbol
                            used_positions.update(range(match_start, match_end))
                            matched = True
                            break  # 只匹配第一个出现的位置
                    if matched:
                        break
                except re.error:
                    continue

        return found_symbols

    def _extract_english_phrases_enhanced(self, text: str) -> List[Tuple[str, int, int]]:
        """
        从文本中提取英文短语 - 完全采用news_symbol_matcher.py的逻辑
        从原始文本中提取短语，但在处理后的文本中查找位置
        """
        phrases = []

        # 在原始文本中提取短语（保持大小写）
        multi_word_pattern = r'([A-Z][a-zA-Z0-9]*(?:\s+[A-Z][a-zA-Z0-9]*){1,3})(?=[\s\u4e00-\u9fff：，。！？；]|$)'

        for match in re.finditer(multi_word_pattern, text):
            phrase = match.group(1).lower()
            # 在处理后的文本中查找对应位置（这里text已经是处理后的小写文本）
            phrase_in_processed = phrase.replace(' ', ' ')  # 保持空格
            pos = text.find(phrase_in_processed)
            if pos != -1:
                phrases.append((phrase, pos, pos + len(phrase_in_processed)))

        return phrases

    def _preprocess_special_patterns(self, text: str) -> List[str]:
        """预处理特殊模式（基于news_symbol_matcher.py的逻辑）"""
        additional_keywords = []

        # 定义特殊模式：代币符号 + 后缀
        special_patterns = [
            r'([A-Z]{2,})\s*token',      # BTC token -> btc
            r'([A-Z]{2,})\s*coin',       # BTC coin -> btc
            r'([A-Z]{2,})\s*network',    # BTC network -> btc
            r'([A-Z]{2,})\s*protocol',   # BTC protocol -> btc
            r'([A-Z]{2,})USD(?:T)?',     # BTCUSD, BTCUSDT -> btc
            r'([A-Z]{2,})\s*u本位',      # BTC U本位 -> btc
        ]

        for pattern in special_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                core_symbol = match.group(1).lower()

                # 检查是否在关键词库中
                if core_symbol in self.symbol_keywords:
                    additional_keywords.append(core_symbol)

                # 检查是否在binance symbols中
                binance_symbol = core_symbol.upper() + 'USDT'
                if binance_symbol in self.binance_symbols:
                    additional_keywords.append(core_symbol)

        return list(set(additional_keywords))  # 去重

    def _calculate_symbol_score(self, keyword: str, title: str, content: str) -> float:
        """计算符号相关性评分（基于news_symbol_matcher.py的逻辑）"""
        # 基础分数：标题中出现 = 3分
        score = 3.0

        # 内容验证：内容中出现次数
        content_count = self._verify_symbol_in_content(keyword, content)
        if content_count >= 1:
            score += content_count * 2.0  # 内容中每次出现+2分
        else:
            # 如果内容中没有出现，降分
            score -= 2.0

        # 特殊模式奖励
        pattern_bonus = self._calculate_special_pattern_bonus(keyword, title, content)
        score += pattern_bonus

        return score

    def _verify_symbol_in_content(self, symbol_keyword: str, content: str) -> int:
        """验证符号在内容中的出现次数（基于news_symbol_matcher.py的逻辑）"""
        content_lower = content.lower()

        if any('\u4e00' <= char <= '\u9fff' for char in symbol_keyword):
            # 中文关键词
            count = content_lower.count(symbol_keyword)
        else:
            # 英文关键词，使用词边界匹配
            pattern = r'\b' + re.escape(symbol_keyword) + r'\b'
            matches = re.findall(pattern, content_lower)
            count = len(matches)

        return count

    def _calculate_special_pattern_bonus(self, keyword: str, title: str, content: str) -> float:
        """检查特殊模式并给予额外分数（基于news_symbol_matcher.py的逻辑）"""
        full_text = (title + " " + content).lower()
        bonus = 0.0

        # 特殊模式列表
        special_patterns = [
            f'{keyword}token',      # **token
            f'{keyword}coin',       # **coin
            f'{keyword}代币',       # **代币
            f'{keyword}空投',       # **空投
            f'空投{keyword}',       # 空投**
            f'{keyword}解锁',       # **解锁
            f'{keyword}network',    # **network
            f'{keyword}protocol',   # **protocol
            f'{keyword}usd',        # **usd
            f'{keyword}usdt',       # **usdt
            f'{keyword}u本位',      # **U本位
            f'u本位{keyword}',      # U本位**
        ]

        for pattern in special_patterns:
            if pattern in full_text:
                if '空投' in pattern or '解锁' in pattern:
                    bonus += 3.0  # 空投和解锁给更高分数
                elif 'token' in pattern or '代币' in pattern or 'u本位' in pattern:
                    bonus += 2.0  # token、代币、U本位给高分数
                else:
                    bonus += 1.0  # 其他模式给基础分数

        return bonus

    def _calculate_confidence_from_score(self, score: float, has_trading_context: bool) -> float:
        """从评分计算置信度"""
        # 将评分转换为置信度 (0.0-1.0)
        base_confidence = min(score / 10.0, 1.0)  # 10分对应100%置信度

        # 交易上下文加成
        if has_trading_context:
            base_confidence *= self.confidence_params['trading_context_bonus']

        return min(base_confidence, self.confidence_params['max_confidence'])

    def _has_trading_context(self, text: str) -> bool:
        """检查文本是否包含交易相关内容（使用预编译正则）"""
        if self._trading_keywords_compiled is None:
            return False
        return bool(self._trading_keywords_compiled.search(text))
    
    def _extract_full_symbols(self, text: str) -> Counter:
        """提取完整的symbols（使用预编译正则）"""
        matches = Counter()
        for match in self._full_symbol_pattern.finditer(text):
            matches[match.group(1)] += 1
        return matches
    
    def _extract_base_tokens_from_text(self, text: str) -> Counter:
        """从文本中提取基础代币名称（使用预编译正则）"""
        matches = Counter()
        for match in self._base_token_pattern.finditer(text):
            base_token = match.group(1)
            full_symbol = self.base_tokens[base_token]
            matches[full_symbol] += 1
        return matches
    
    def _extract_mapped_symbols(self, text: str) -> Counter:
        """通过映射表提取symbols（使用预编译正则）"""
        matches = Counter()
        for match in self._mapped_symbol_pattern.finditer(text):
            keyword = match.group(0).lower()
            base_token = self.legacy_symbol_mapping.get(keyword)
            if base_token:
                full_symbol = self.base_tokens.get(base_token)
                if full_symbol:
                    matches[full_symbol] += 1
        return matches
    
    def _filter_and_rank_symbols(self, symbol_matches: Counter, has_trading_context: bool,
                                extraction_sources: Dict[str, str] = None) -> List[Tuple[str, int, float]]:
        """
        过滤和排序symbols，并还原为大写

        Args:
            symbol_matches: symbol匹配计数
            has_trading_context: 是否有交易上下文
            extraction_sources: symbol的提取来源映射

        Returns:
            List[Tuple[str, int, float]]: (大写symbol, 匹配次数, 置信度)
        """
        results = []
        extraction_sources = extraction_sources or {}

        for lower_symbol, count in symbol_matches.items():
            # 跳过黑名单（稳定币已在黑名单中）
            if lower_symbol in self.blacklist:
                continue

            # 获取提取来源
            source = extraction_sources.get(lower_symbol, 'unknown')

            # 计算置信度
            confidence = self._calculate_confidence(lower_symbol, count, has_trading_context, source)

            # 使用参数化阈值
            min_confidence = (self.threshold_params['min_confidence_with_context']
                            if has_trading_context
                            else self.threshold_params['min_confidence_without_context'])

            if confidence >= min_confidence:
                # 根据命名规则恢复：全部转为大写
                upper_symbol = lower_symbol.upper()
                results.append((upper_symbol, count, confidence))

        # 按置信度排序，置信度相同时按匹配次数排序
        results.sort(key=lambda x: (x[2], x[1]), reverse=True)

        return results


    
    def _calculate_confidence(self, lower_symbol: str, match_count: int, has_trading_context: bool,
                             extraction_source: str = 'unknown') -> float:
        """
        计算提取置信度（基于小写symbol）

        Args:
            lower_symbol: 小写的symbol
            match_count: 匹配次数
            has_trading_context: 是否有交易上下文
            extraction_source: 提取来源 ('mapping', 'base_token', 'full_symbol')

        Returns:
            float: 置信度 (0.0-1.0)
        """
        if match_count < self.threshold_params['min_match_count']:
            return 0.0

        # 基础置信度：基于匹配次数
        base_confidence = min(match_count * self.confidence_params['base_multiplier'],
                             self.confidence_params['max_confidence'])

        # 根据symbol特征调整置信度
        base_token = self._extract_base_token_from_symbol(lower_symbol)

        # 交易上下文加成
        if has_trading_context:
            base_confidence *= self.confidence_params['trading_context_bonus']

        # 根据提取来源调整
        base_confidence *= self._get_source_multiplier(extraction_source)

        # 特殊规则：多次匹配的额外加成
        if match_count >= 3:
            base_confidence *= 1.1

        return min(base_confidence, self.confidence_params['max_confidence'])

    def _extract_base_token_from_symbol(self, lower_symbol: str) -> str:
        """从symbol中提取基础代币名称"""
        # 移除常见的交易对后缀
        for suffix in ['usdt', 'usdc', 'btc', 'eth', 'bnb']:
            if lower_symbol.endswith(suffix):
                return lower_symbol[:-len(suffix)]
        return lower_symbol

    def _get_source_multiplier(self, extraction_source: str) -> float:
        """根据提取来源获取置信度倍数"""
        source_multipliers = {
            'mapping': 1.0,      # 映射表来源（最可靠）
            'base_token': 0.95,  # 基础代币匹配
            'full_symbol': 0.9,  # 完整symbol匹配
            'unknown': 0.8       # 未知来源
        }
        return source_multipliers.get(extraction_source, 0.8)
    
    def extract_instrument_ids(self, preprocessed_title: str, preprocessed_content: str) -> List[InstrumentId]:
        """
        从预处理后的文本中提取InstrumentId列表

        Args:
            preprocessed_title: 预处理后的标题（已转小写）
            preprocessed_content: 预处理后的内容（已转小写）

        Returns:
            List[InstrumentId]: 按置信度排序的instrument列表
        """
        symbols_with_confidence = self.extract_symbols_from_text(preprocessed_title, preprocessed_content)
        instrument_ids = []

        for symbol, count, confidence in symbols_with_confidence:
            # 构造完整的instrument_id: BTCUSDT-PERP.BINANCE
            instrument_str = f"{symbol}-PERP.{self.venue.value}"
            try:
                instrument_id = InstrumentId.from_str(instrument_str)
                instrument_ids.append(instrument_id)
            except Exception as e:
                # 如果构造失败，跳过这个symbol
                continue

        return instrument_ids

    def get_config_info(self) -> Dict:
        """获取配置信息"""
        return {
            'confidence_params': self.confidence_params,
            'threshold_params': self.threshold_params,
            'max_symbols': self.max_symbols,
            'venue': str(self.venue),
            'available_symbols_count': len(self.available_symbols),
            'base_tokens_count': len(self.base_tokens),
            'legacy_symbol_mapping_count': len(self.legacy_symbol_mapping),
            'blacklist_count': len(self.blacklist)
        }

    def update_confidence_params(self, new_params: Dict) -> None:
        """更新置信度参数"""
        self.confidence_params.update(new_params)

    def update_threshold_params(self, new_params: Dict) -> None:
        """更新阈值参数"""
        self.threshold_params.update(new_params)



def process_news_file(args):
    """
    处理单个新闻文件，用于并行化。
    """
    news_file, news_dir, news_data_filtered_dir, extractor_params = args
    import pandas as pd
    import os

    # 在子进程中重新创建提取器实例
    extractor = NewsInstrumentExtractor(
        cache=None,
        venue=Venue(extractor_params['venue']),
        max_symbols=extractor_params['max_symbols'],
        confidence_params=extractor_params['confidence_params'],
        threshold_params=extractor_params['threshold_params'],
        binance_symbols_path=extractor_params.get('binance_symbols_path'),
        crypto_aliases_path=extractor_params.get('crypto_aliases_path')
    )
    extractor._set_available_symbols(set(extractor_params['available_symbols']))

    input_path = os.path.join(news_dir, news_file)
    output_path = os.path.join(news_data_filtered_dir, news_file)

    try:
        df = pd.read_csv(input_path, encoding='utf-8')

        def extract_symbols_for_row(row):
            title = str(row.get('title', '')).lower()
            content = str(row.get('content', '')).lower()
            symbols_with_confidence = extractor.extract_symbols_from_text(title, content)
            if symbols_with_confidence:
                return ','.join([s[0] for s in symbols_with_confidence])
            return ''

        df['relative_symbols'] = df.apply(extract_symbols_for_row, axis=1)
        
        df_filtered = df[df['relative_symbols'] != ''].copy()

        if not df_filtered.empty:
            df_filtered.to_csv(output_path, index=False, encoding='utf-8-sig')
        
        return len(df), len(df_filtered)

    except Exception as e:
        print(f"处理文件 {news_file} 时出错: {e}")
        return 0, 0

def process_news_data():
    """
    主要处理函数：从新闻数据中提取symbols并保存到过滤后的目录（并行优化版）
    """
    import pandas as pd
    import json
    import os
    from pathlib import Path
    from multiprocessing import Pool, cpu_count
    from tqdm import tqdm

    # 加载 Binance symbols
    with open('binance_symbols.json', 'r') as f:
        binance_data = json.load(f)
    available_symbols = list(binance_data.keys())

    # 提取器参数
    extractor_params = {
        'venue': "BINANCE",
        'max_symbols': 5,
        'confidence_params': {
            'base_multiplier': 0.25,
            'trading_context_bonus': 1.15,
            'max_confidence': 1.0
        },
        'threshold_params': {
            'min_confidence_with_context': 0.1,
            'min_confidence_without_context': 0.1,
            'high_confidence_threshold': 0.6,
            'min_match_count': 1
        },
        'available_symbols': available_symbols,
        'binance_symbols_path': 'binance_symbols.json',
        'crypto_aliases_path': 'alias_symbols_processed.txt'
    }

    # 创建输出目录
    news_dir = "news_data"
    news_data_filtered_dir = "filter_news"
    Path(news_data_filtered_dir).mkdir(exist_ok=True)

    news_files = sorted([f for f in os.listdir(news_dir) if f.endswith('.csv')])
    
    # 设置并行处理
    num_processes = cpu_count()
    pool_args = [(f, news_dir, news_data_filtered_dir, extractor_params) for f in news_files]

    total_processed = 0
    total_with_symbols = 0

    print(f"开始使用 {num_processes} 个进程处理 {len(news_files)} 个新闻文件...")

    with Pool(processes=num_processes) as pool:
        results = list(tqdm(pool.imap(process_news_file, pool_args), total=len(news_files)))

    for processed_count, with_symbols_count in results:
        total_processed += processed_count
        total_with_symbols += with_symbols_count

    print(f"\n处理完成!")
    if total_processed > 0:
        print(f"总计处理: {total_processed} 条新闻")
        print(f"提取到symbols: {total_with_symbols} 条新闻")
        print(f"过滤率: {(total_with_symbols / total_processed) * 100:.2f}%")
    else:
        print("没有处理任何新闻。")


def test_single_file():
    """测试单个文件的处理"""
    import pandas as pd
    import json

    # 加载 Binance symbols
    with open('binance_symbols.json', 'r') as f:
        binance_data = json.load(f)

    available_symbols = set(binance_data.keys())

    # 创建提取器实例
    extractor = NewsInstrumentExtractor(
        cache=None,
        venue=Venue("BINANCE"),
        max_symbols=5,
        confidence_params={
            'base_multiplier': 0.25,
            'trading_context_bonus': 1.15,
            'max_confidence': 1.0
        },
        threshold_params={
            'min_confidence_with_context': 0.15,
            'min_confidence_without_context': 0.25,
            'high_confidence_threshold': 0.6,
            'min_match_count': 1
        },
        binance_symbols_path='binance_symbols.json',
        crypto_aliases_path='alias_symbols_processed.txt'
    )

    extractor._set_available_symbols(available_symbols)

    # 测试单个文件
    test_file = "news_data/panews_flash_20250101.csv"
    df = pd.read_csv(test_file, encoding='utf-8')

    print(f"原始数据列: {list(df.columns)}")
    print(f"原始数据行数: {len(df)}")

    # 添加 relative_symbols 列
    df['relative_symbols'] = ''

    # 处理前几行作为测试
    for idx in range(min(5, len(df))):
        row = df.iloc[idx]
        title = str(row.get('title', '')).lower()
        content = str(row.get('content', '')).lower()

        print(f"\n处理第 {idx+1} 行:")
        print(f"标题: {row.get('title', '')[:50]}...")

        symbols_with_confidence = extractor.extract_symbols_from_text(title, content)

        if symbols_with_confidence:
            symbols = [symbol for symbol, count, confidence in symbols_with_confidence]
            df.at[idx, 'relative_symbols'] = ','.join(symbols)
            print(f"提取到的symbols: {symbols}")
        else:
            print("未提取到symbols")

    # 检查结果
    print(f"\n处理后数据列: {list(df.columns)}")
    print(f"有symbols的行数: {len(df[df['relative_symbols'] != ''])}")

    # 保存测试结果
    test_output = "test_output.csv"
    df_filtered = df[df['relative_symbols'] != ''].copy()
    df_filtered.to_csv(test_output, index=False, encoding='utf-8-sig')
    print(f"测试结果已保存到: {test_output}")


def test_optimized_extractor():
    """测试优化后的提取器"""
    import json

    # 加载 Binance symbols
    try:
        with open('binance_symbols.json', 'r') as f:
            binance_data = json.load(f)
    except FileNotFoundError:
        print("Warning: binance_symbols.json not found, using empty data")
        binance_data = {}

    available_symbols = set(binance_data.keys())

    # 创建提取器实例
    extractor = NewsInstrumentExtractor(
        cache=None,
        venue=Venue("BINANCE"),
        max_symbols=3,
        confidence_params={
            'base_multiplier': 0.3,
            'trading_context_bonus': 1.2,
            'max_confidence': 1.0
        },
        threshold_params={
            'min_confidence_with_context': 0.15,
            'min_confidence_without_context': 0.25,
            'min_match_count': 1
        },
        binance_symbols_path='binance_symbols.json',
        crypto_aliases_path='alias_symbols_processed.txt'
    )

    extractor._set_available_symbols(available_symbols)

    # 测试用例
    test_cases = [
        {
            'title': 'BTC价格突破50000美元，比特币迎来新高',
            'content': '比特币(BTC)今日价格突破50000美元大关，创下历史新高。交易量大幅增加。',
            'expected_symbols': ['BTCUSDT']
        },
        {
            'title': 'Ethereum升级完成，ETH代币价格上涨',
            'content': '以太坊网络升级顺利完成，ETH代币价格应声上涨，交易活跃度显著提升。',
            'expected_symbols': ['ETHUSDT']
        },
        {
            'title': 'Portal Ventures投资新项目',
            'content': 'Portal Ventures宣布投资一个新的DeFi项目，该项目将在下个月上线。',
            'expected_symbols': []  # Portal应该被正确识别为短语，不应该单独匹配
        },
        {
            'title': 'DOGE空投活动开始，狗狗币持有者受益',
            'content': '狗狗币(DOGE)空投活动正式开始，持有DOGE的用户可以获得免费代币。',
            'expected_symbols': ['DOGEUSDT']
        }
    ]

    print("测试优化后的符号提取器...")
    print("=" * 50)

    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}:")
        print(f"标题: {test_case['title']}")
        print(f"内容: {test_case['content'][:50]}...")

        # 提取符号
        title_lower = test_case['title'].lower()
        content_lower = test_case['content'].lower()

        symbols_with_confidence = extractor.extract_symbols_from_text(title_lower, content_lower)

        extracted_symbols = [symbol for symbol, count, confidence in symbols_with_confidence]

        print(f"提取到的符号: {extracted_symbols}")
        print(f"期望的符号: {test_case['expected_symbols']}")

        # 检查结果
        if set(extracted_symbols) == set(test_case['expected_symbols']):
            print("✅ 测试通过")
        else:
            print("❌ 测试失败")

        # 显示详细信息
        if symbols_with_confidence:
            print("详细信息:")
            for symbol, count, confidence in symbols_with_confidence:
                print(f"  - {symbol}: 匹配次数={count}, 置信度={confidence:.3f}")

    print("\n" + "=" * 50)
    print("测试完成")

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "test":
        # 运行测试
        test_optimized_extractor()
    elif len(sys.argv) > 1 and sys.argv[1] == "test_single":
        # 运行单文件测试
        test_single_file()
    else:
        # 运行完整处理
        process_news_data()
    
