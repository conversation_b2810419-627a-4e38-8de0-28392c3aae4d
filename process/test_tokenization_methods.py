#!/usr/bin/env python3
"""
测试不同分词方法的简化版本
"""

import pandas as pd
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score
import os

def test_basic_functionality():
    """测试基本功能"""
    print("=== 测试基本功能 ===")
    
    # 1. 加载数据
    print("1. 加载数据...")
    df = pd.read_csv('news_training_samples_complete_all.csv')
    print(f"   数据形状: {df.shape}")
    
    # 2. 过滤有效数据
    df = df[df['label'] != 'TBD'].copy()
    df = df.dropna(subset=['news_title', 'news_content', 'label'])
    
    numeric_columns = ['label', 'natr', 'mfi', 'rsi', 'btc_natr', 'btc_mfi', 'btc_rsi']
    for col in numeric_columns:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
    
    df = df.dropna(subset=numeric_columns)
    print(f"   有效数据: {len(df)} 条")
    
    if len(df) < 100:
        print("   数据太少，无法进行有效测试")
        return None
    
    # 3. 创建分类标签
    returns = df['label'].values
    quantiles = np.percentile(returns, [20, 40, 60, 80])
    
    def categorize_return(ret):
        if ret <= quantiles[0]:
            return 0
        elif ret <= quantiles[1]:
            return 1
        elif ret <= quantiles[2]:
            return 2
        elif ret <= quantiles[3]:
            return 3
        else:
            return 4
    
    df['return_category'] = df['label'].apply(categorize_return)
    
    # 4. 准备文本
    df['combined_text'] = df['news_title'].fillna('') + ' ' + df['news_content'].fillna('')
    
    print(f"   分类分布: {df['return_category'].value_counts().sort_index().to_dict()}")
    
    return df

def test_jieba_tokenization(df):
    """测试jieba分词"""
    print("\n=== 测试jieba分词 ===")
    
    try:
        import jieba
        
        # 取样本数据
        sample_df = df.sample(min(1000, len(df)), random_state=42)
        texts = sample_df['combined_text'].values
        y = sample_df['return_category'].values
        
        # jieba分词
        def jieba_tokenize(text):
            return ' '.join(jieba.cut(text))
        
        print("   进行jieba分词...")
        tokenized_texts = [jieba_tokenize(text) for text in texts]
        
        # TF-IDF特征
        vectorizer = TfidfVectorizer(max_features=500, min_df=2, max_df=0.8)
        X_text = vectorizer.fit_transform(tokenized_texts)
        
        # 技术指标特征
        technical_cols = ['natr', 'mfi', 'rsi', 'btc_natr', 'btc_mfi', 'btc_rsi']
        X_tech = sample_df[technical_cols].values
        
        # 合并特征
        X = np.hstack([X_text.toarray(), X_tech])
        
        # 训练测试
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
        
        model = RandomForestClassifier(n_estimators=50, random_state=42)
        model.fit(X_train, y_train)
        
        accuracy = model.score(X_test, y_test)
        print(f"   jieba分词准确率: {accuracy:.4f}")
        
        return accuracy
        
    except ImportError:
        print("   jieba未安装")
        return None
    except Exception as e:
        print(f"   jieba测试失败: {e}")
        return None

def test_sentencepiece_tokenization(df):
    """测试SentencePiece分词"""
    print("\n=== 测试SentencePiece分词 ===")
    
    try:
        import sentencepiece as spm
        
        model_path = "models/crypto_tokenizer_medium_with_symbols.model"
        if not os.path.exists(model_path):
            print("   SentencePiece模型不存在")
            return None
        
        # 加载模型
        sp = spm.SentencePieceProcessor()
        sp.load(model_path)
        
        # 取样本数据
        sample_df = df.sample(min(1000, len(df)), random_state=42)
        texts = sample_df['combined_text'].values
        y = sample_df['return_category'].values
        
        # SentencePiece分词
        def sp_tokenize(text):
            return ' '.join(sp.encode_as_pieces(text))
        
        print("   进行SentencePiece分词...")
        tokenized_texts = [sp_tokenize(text) for text in texts]
        
        # TF-IDF特征
        vectorizer = TfidfVectorizer(max_features=500, min_df=2, max_df=0.8)
        X_text = vectorizer.fit_transform(tokenized_texts)
        
        # 技术指标特征
        technical_cols = ['natr', 'mfi', 'rsi', 'btc_natr', 'btc_mfi', 'btc_rsi']
        X_tech = sample_df[technical_cols].values
        
        # 合并特征
        X = np.hstack([X_text.toarray(), X_tech])
        
        # 训练测试
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
        
        model = RandomForestClassifier(n_estimators=50, random_state=42)
        model.fit(X_train, y_train)
        
        accuracy = model.score(X_test, y_test)
        print(f"   SentencePiece分词准确率: {accuracy:.4f}")
        
        return accuracy
        
    except ImportError:
        print("   sentencepiece未安装")
        return None
    except Exception as e:
        print(f"   SentencePiece测试失败: {e}")
        return None

def test_word2vec_features(df):
    """测试Word2Vec特征"""
    print("\n=== 测试Word2Vec特征 ===")
    
    try:
        from gensim.models import Word2Vec
        from gensim.utils import simple_preprocess
        
        # 取样本数据
        sample_df = df.sample(min(1000, len(df)), random_state=42)
        texts = sample_df['combined_text'].values
        y = sample_df['return_category'].values
        
        # 预处理文本
        sentences = [simple_preprocess(text) for text in texts]
        
        print("   训练Word2Vec模型...")
        model = Word2Vec(
            sentences=sentences,
            vector_size=50,  # 减小向量维度以加快训练
            window=5,
            min_count=2,
            workers=2,
            epochs=5
        )
        
        # 创建文档向量
        def get_doc_vector(words):
            vectors = []
            for word in words:
                if word in model.wv:
                    vectors.append(model.wv[word])
            
            if vectors:
                return np.mean(vectors, axis=0)
            else:
                return np.zeros(model.vector_size)
        
        print("   生成文档向量...")
        X_text = np.array([get_doc_vector(simple_preprocess(text)) for text in texts])
        
        # 技术指标特征
        technical_cols = ['natr', 'mfi', 'rsi', 'btc_natr', 'btc_mfi', 'btc_rsi']
        X_tech = sample_df[technical_cols].values
        
        # 合并特征
        X = np.hstack([X_text, X_tech])
        
        # 训练测试
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
        
        model_rf = RandomForestClassifier(n_estimators=50, random_state=42)
        model_rf.fit(X_train, y_train)
        
        accuracy = model_rf.score(X_test, y_test)
        print(f"   Word2Vec特征准确率: {accuracy:.4f}")
        
        return accuracy
        
    except ImportError:
        print("   gensim未安装")
        return None
    except Exception as e:
        print(f"   Word2Vec测试失败: {e}")
        return None

def test_default_tfidf(df):
    """测试默认TF-IDF"""
    print("\n=== 测试默认TF-IDF ===")
    
    try:
        # 取样本数据
        sample_df = df.sample(min(1000, len(df)), random_state=42)
        texts = sample_df['combined_text'].values
        y = sample_df['return_category'].values
        
        # 默认TF-IDF
        vectorizer = TfidfVectorizer(max_features=500, min_df=2, max_df=0.8, ngram_range=(1, 2))
        X_text = vectorizer.fit_transform(texts)
        
        # 技术指标特征
        technical_cols = ['natr', 'mfi', 'rsi', 'btc_natr', 'btc_mfi', 'btc_rsi']
        X_tech = sample_df[technical_cols].values
        
        # 合并特征
        X = np.hstack([X_text.toarray(), X_tech])
        
        # 训练测试
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
        
        model = RandomForestClassifier(n_estimators=50, random_state=42)
        model.fit(X_train, y_train)
        
        accuracy = model.score(X_test, y_test)
        print(f"   默认TF-IDF准确率: {accuracy:.4f}")
        
        return accuracy
        
    except Exception as e:
        print(f"   默认TF-IDF测试失败: {e}")
        return None

def main():
    """主函数"""
    print("🚀 开始分词方法对比测试")
    
    # 测试基本功能
    df = test_basic_functionality()
    if df is None:
        return
    
    # 测试各种方法
    results = {}
    
    results['jieba'] = test_jieba_tokenization(df)
    results['sentencepiece'] = test_sentencepiece_tokenization(df)
    results['word2vec'] = test_word2vec_features(df)
    results['default_tfidf'] = test_default_tfidf(df)
    
    # 总结结果
    print("\n=== 结果总结 ===")
    valid_results = {k: v for k, v in results.items() if v is not None}
    
    if valid_results:
        best_method = max(valid_results.items(), key=lambda x: x[1])
        print(f"🏆 最佳方法: {best_method[0]} (准确率: {best_method[1]:.4f})")
        
        print("\n📊 所有结果:")
        for method, accuracy in sorted(valid_results.items(), key=lambda x: x[1], reverse=True):
            print(f"  {method}: {accuracy:.4f}")
    else:
        print("❌ 没有有效的测试结果")
    
    print("\n✅ 测试完成！")

if __name__ == "__main__":
    main()
